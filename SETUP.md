# 📱 صندوق العشيرة - Family Fund Box

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK
- Firebase Account
- Android Studio / VS Code

### 1. Firebase Project Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Click "Add project" and follow the setup wizard
   - Give your project a name (e.g., "family-fund-box")

2. **Enable Firestore Database**
   - In your Firebase project, go to "Firestore Database"
   - Click "Create database"
   - Start in "Test mode" for development
   - Choose a location for your database

3. **Add Android App**
   - In project settings, go to "Your apps"
   - Click the Android icon to add an Android app
   - Follow the setup instructions
   - Download the `google-services.json` file
   - Place it in `android/app/` directory

4. **Add iOS App (Optional)**
   - Similarly, add an iOS app if needed
   - Download the `GoogleService-Info.plist` file
   - Place it in `ios/Runner/` directory

### 2. Flutter Setup

1. **Install Dependencies**
   ```bash
   flutter pub get
   ```

2. **Update Firebase Configuration**
   - Replace placeholder values in `lib/firebase_options.dart`
   - Add your actual Firebase configuration

3. **Run the Application**
   ```bash
   flutter run
   ```

### 3. Database Collections

The app uses the following Firestore collections:

#### Families Collection
```json
{
  "name": "string",
  "members_count": "number"
}
```

#### Payments Collection
```json
{
  "family_id": "string",
  "amount": "number",
  "month": "number",
  "year": "number",
  "is_paid": "boolean"
}
```

#### Expenses Collection
```json
{
  "amount": "number",
  "description": "string",
  "date": "timestamp"
}
```

#### Settings Document
```json
{
  "amount_per_person": "number"
}
```

## 📱 Features

### Core Functionality
- ✅ Family Management (Add, Edit, Delete)
- ✅ Monthly Payment Tracking
- ✅ Expense Management
- ✅ Fund Balance Calculation
- ✅ Settings Configuration
- ✅ Statistics & Reports
- ✅ Arabic Localization (RTL)
- ✅ Cloud Data Sync

### Screens
1. **Home/Dashboard** - Fund overview and quick stats
2. **Families** - Complete family management
3. **Payments** - Monthly payment tracking
4. **Expenses** - Expense recording and management
5. **Settings** - Configuration options
6. **Statistics** - Visual reports and analytics

## 🛠️ Development

### Project Structure
```
lib/
├── models/           # Data models
├── providers/        # State management
├── screens/          # UI screens
└── main.dart         # App entry point
```

### Key Technologies
- Flutter
- Firebase Firestore
- Provider Pattern
- Material Design 3
- fl_chart for visualizations

## 📊 Usage Guide

### 1. Adding Families
- Go to Families screen
- Click the "+" button
- Enter family name and member count
- Save the family

### 2. Tracking Payments
- Go to Payments screen
- Select the month/year
- Toggle payment status for each family
- View payment summary

### 3. Managing Expenses
- Go to Expenses screen
- Click "+" to add new expense
- Enter amount and description
- Save the expense

### 4. Viewing Statistics
- Go to Statistics screen
- View fund balance overview
- Check monthly charts
- See payment status distribution

## 🔧 Troubleshooting

### Common Issues

1. **Firebase Connection Issues**
   - Check Firebase configuration
   - Verify internet connection
   - Ensure Firestore rules allow read/write

2. **Dependency Conflicts**
   - Run `flutter clean`
   - Update dependencies with `flutter pub get`

3. **RTL Issues**
   - Ensure device language is set to Arabic
   - Check app localization settings

## 📞 Support

For support and questions:
- Email: <EMAIL>
- GitHub: [Project Repository]
- Documentation: [Link to docs]

---

© 2024 صندوق العشيرة. All rights reserved.
